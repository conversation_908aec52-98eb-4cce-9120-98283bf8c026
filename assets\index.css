@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;500;600&amp;display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  font-family: "Noto Sans", sans-serif;
}

:root {
  --text-color: #21222c;
  --bg-color: #fff;
  --forground-color: #f4f4f6;
  --white-color: #fff;
  --black-color: #000;
  --primary-color: #2eaa52;
  --error-color: #ff0000;
  --border-color: #ebebf0;
  --gray-color: #515152;
  --header-footer-color: rgb(199 231 208);
}

html[data-theme="dark"] {
  --header-footer-color: #21222c;
  --text-color: #f4f4f6;
  --bg-color: #13141a;
  --forground-color: #21222c;
  --white-color: #fff;
  --black-color: #000;
  --primary-color: #2eaa52;
  --error-color: #ff0000;
  --border-color: #282a36;
  --gray-color: #ababae;
}

body {
  font-family: "Noto Sans", sans-serif;
  background: var(--bg-color);
  color: var(--text-color);
  line-height: 1.5;
  font-size: 15px;
  padding-top: 90px;
}

body * {
  color: inherit;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

a:hover {
  text-decoration: underline;
}

img {
  width: 100%;
  display: block;
}

.container {
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 16px;
}

.header {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: 1000;
  padding: 0 16px;
  background-color: var(--header-footer-color);
  box-shadow: 0 5px 42px 0 #00000014;
  backdrop-filter: blur(10px);
}

.header .container {
  height: 70px;
  display: flex;
  align-items: center;
  padding: 0 1.25rem;
  /* background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 5px 42px 0 #00000014;
  backdrop-filter: blur(10px); */
  border-radius: 1rem;
}

/* html[data-theme="dark"] .header .container {
  background-color: rgba(0, 0, 0, 0.8);
} */

.header__logo a,
.site__logo a {
  display: flex;
  color: var(--text-color);
  font-size: 1rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  font-weight: 700;
  background: url("images/logo.png") center left no-repeat;
  background-size: contain;
  height: 35px;
  padding-left: 45px;
  letter-spacing: 0.5px;
}

.header__logo a img {
  width: 40px;
  margin-right: 1rem;
}

.site__logo a {
  height: 50px;
  padding-left: 60px;
  font-size: 1.2rem;
}

.header__menu {
  margin-left: auto;
}

.header__menu_list {
  display: flex;
  align-items: center;
  list-style: none;
}

.header__menu_list li {
  margin-left: 0.5rem;
  position: relative;
}

#compressBtn,
#compressPDF {
  position: relative;
  transition: 0.3s all;
  display: flex;
  align-items: center;
}

#compressBtn::before,
#compressPDF::before {
  content: "";
  display: block;
  margin-right: 10px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid #fff;
  border-bottom-color: transparent;
  animation: spin 1s linear infinite;
  display: none;
}

body.loading__compressing #compressBtn::before,
body.loading__compressing #compressPDF::before {
  display: block;
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.header__menu_dropdown {
  position: absolute;
  top: calc(100% + 10px);
  background: var(--bg-color);
  box-shadow: 0 5px 42px 0 #00000014;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  padding: 0.5rem 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all;
  list-style: none;
  width: 226px;
  padding: 10px;
  right: 0;
}

.mobile_only {
  display: none;
}

.header__menu_dropdown::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 20px;
  top: -10px;
  opacity: 0;
}

.header__menu_dropdown li {
  margin: 0;
}

.header__menu_dropdown li a {
  color: var(--text-color);
  padding: 0.4rem 0.6rem !important;
  display: block;
  transition: 0.3s all;
  border-radius: 5px;
}

.header__menu_list li:hover .header__menu_dropdown {
  opacity: 1;
  visibility: visible;
}

.header__menu_list li a,
.header__menu_list li span {
  color: var(--text-color);
  padding: 0.5rem 0.5rem;
  cursor: pointer;
  font-weight: 600;
  transition: 0.3s all;
  font-size: 14px;
}

.header__menu_list li a:hover,
.header__menu_list li span:hover,
.header__menu_list li.active > a,
.header__menu_list li.active > span {
  color: var(--primary-color);
  text-decoration: none;
}

.toggle_button {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='1em' height='1em' fill='none' aria-hidden='true' focusable='false'%3E%3Cg%3E%3Cpath d='M21.0672 11.8568L20.4253 11.469L21.0672 11.8568ZM12.1432 2.93276L11.7553 2.29085V2.29085L12.1432 2.93276ZM21.25 12C21.25 17.1086 17.1086 21.25 12 21.25V22.75C17.9371 22.75 22.75 17.9371 22.75 12H21.25ZM12 21.25C6.89137 21.25 2.75 17.1086 2.75 12H1.25C1.25 17.9371 6.06294 22.75 12 22.75V21.25ZM2.75 12C2.75 6.89137 6.89137 2.75 12 2.75V1.25C6.06294 1.25 1.25 6.06294 1.25 12H2.75ZM15.5 14.25C12.3244 14.25 9.75 11.6756 9.75 8.5H8.25C8.25 12.5041 11.4959 15.75 15.5 15.75V14.25ZM20.4253 11.469C19.4172 13.1373 17.5882 14.25 15.5 14.25V15.75C18.1349 15.75 20.4407 14.3439 21.7092 12.2447L20.4253 11.469ZM9.75 8.5C9.75 6.41182 10.8627 4.5828 12.531 3.57467L11.7553 2.29085C9.65609 3.5593 8.25 5.86509 8.25 8.5H9.75ZM12 2.75C11.9115 2.75 11.8077 2.71008 11.7324 2.63168C11.6686 2.56527 11.6538 2.50244 11.6503 2.47703C11.6461 2.44587 11.6482 2.35557 11.7553 2.29085L12.531 3.57467C13.0342 3.27065 13.196 2.71398 13.1368 2.27627C13.0754 1.82126 12.7166 1.25 12 1.25V2.75ZM21.7092 12.2447C21.6444 12.3518 21.5541 12.3539 21.523 12.3497C21.4976 12.3462 21.4347 12.3314 21.3683 12.2676C21.2899 12.1923 21.25 12.0885 21.25 12H22.75C22.75 11.2834 22.1787 10.9246 21.7237 10.8632C21.286 10.804 20.7293 10.9658 20.4253 11.469L21.7092 12.2447Z' fill='%23000' stroke-width='1.5'%3E%3C/path%3E%3C/g%3E%3C/svg%3E")
    center no-repeat;
  width: 32px;
  height: 32px;
  cursor: pointer;
  background-size: 17px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-left: 20px;
}

html[data-theme="dark"] .toggle_button {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='1em' height='1em' fill='none' aria-hidden='true' focusable='false'%3E%3Cg%3E%3Ccircle cx='12' cy='12' r='5' stroke='%23fff' stroke-width='1.5'%3E%3C/circle%3E%3Cpath d='M12 2V4' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M12 20V22' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M4 12L2 12' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M22 12L20 12' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M19.7778 4.22266L17.5558 6.25424' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M4.22217 4.22266L6.44418 6.25424' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M6.44434 17.5557L4.22211 19.7779' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3Cpath d='M19.7778 19.7773L17.5558 17.5551' stroke='%23fff' stroke-width='1.5' stroke-linecap='round'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
}

.toogle_menu {
  width: 32px;
  height: 32px;
  cursor: pointer;
  background-size: 17px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-left: 20px;
  background-color: transparent;
  position: relative;
  display: none;
}

.toogle_menu span,
.toogle_menu span::before,
.toogle_menu span::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: block;
  width: 20px;
  height: 2px;
  background-color: var(--text-color);
  transition: 0.3s all;
  border-radius: 5px;
}

.toogle_menu span::before {
  top: -4px;
}

.toogle_menu span::after {
  top: 6px;
}

.hero__section_content,
.hero__section_result {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 20px;
}

.hero__section,
.result__section {
  padding: 50px 0 0;
}

.result__section {
  display: none;
}

.comparison__wrapper {
  border-radius: 10px;
  height: 300px;
  background-color: var(--bg-color);
}

.result__info__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  width: 100%;
}

.result__info__wrapper > div {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 10px;
}

.result__info__wrapper > div > span {
  display: flex;
}

.result__info__wrapper > div > span:nth-child(1) {
  font-weight: 700;
  font-size: 14px;
  min-width: 50px;
}

.result__info__wrapper > div > span:nth-child(3) {
  font-weight: 700;
  font-size: 14px;
  min-width: 50px;
}

.result__info__wrapper > div > span:nth-child(2) {
  flex: 1;
  width: 100%;
  height: 10px;
  border-radius: 5px;
  background-color: var(--primary-color);
  margin-top: 3px;
}

.result__info__wrapper > div:nth-child(2) > span:nth-child(2) {
  background-color: transparent;
}

.result__info__wrapper > div:nth-child(2) > span:nth-child(2) span {
  display: flex;
  background-color: green;
  height: 100%;
  border-radius: 50px;
  min-width: 2%;
}

.comparison__wrapper .icv__img-b {
  height: auto !important;
  width: 400px !important;
  min-height: 100%;
  object-fit: cover;
}

.comparison__wrapper .icv__img-a {
  width: 400px !important;
  min-height: 100%;
  object-fit: cover;
}

/* .hero__section .container,
.result__section .container {
  max-width: 800px;
} */
.hero_container{
    background-color: var(--forground-color);
    padding: 40px 15px;
    border-radius: 20px;
    /* box-shadow: 0 0 6px 3px #EEE; */
}
.hero__wrapper,
.result__wrapper {
  padding: 20px;
  border-radius: 20px;
  /* background-color: var(--forground-color); */
  display: flex;
}

.hero__wrapper {
  flex-direction: column;
  padding: 15px 15px 20px
}

.hero__section .left__side {
  width: 100%;
  position: relative;
}

.result__section .left__side {
  width: 55%;
  position: relative;
}

.hero__section .right__side {
  width: 100%;
  padding: 15px 0 0 0;
  flex-direction: row;
}

.hero__section h1 {
  text-align: center;
  font-size: 30px;
}

.hero__section .container > p {
  text-align: center;
  margin-bottom: 25px;
}

.hero__section .right__side.single__page .input__wrapper {
  display: none;
}

.hero__section .right__side .btn {
  max-width: 200px;
}

.hero__section .right__side.single__page .btn {
  max-width: 300px;
}

.result__section .right__side {
  width: 45%;
}

.toogle__full__comparison {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #0000005f;
  padding: 5px;
  border-radius: 5px;
  backdrop-filter: blur(2px);
  display: flex;
  font-size: 25px;
  color: #fff;
  border: 0;
  outline: 0;
  z-index: 5;
  cursor: pointer;
}

.toogle__full__comparison svg {
  transition: 0.3s all;
  transform: scale(0.9);
}

.toogle__full__comparison:hover svg {
  transform: scale(1);
}

.comparison__modal {
  backdrop-filter: blur(5px);
  background-color: #0000005e;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all;
}

.comparison__modal.active {
  opacity: 1;
  visibility: visible;
}

.comparison__modal .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.comparison__modal .comparison__modal_wrapper {
  position: relative;
  z-index: 2;
}

.uploadBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px dashed #2EAA52;
  border-radius: 20px;
  padding: 40px;
  transition: 0.3s all;
  width: 100%;
  cursor: pointer;
  height: 200px;
  background-color: var(--bg-color);
}

.uploadBox.dropHover {
  border-color: var(--primary-color);
}

.uploadBox input {
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  cursor: pointer;
}

.uploadBox label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.uploadBox label svg {
  font-size: 60px;
  margin-bottom: 10px;
  opacity: 0.6;
  transition: 0.3s all;
  stroke: #2EAA52;
}

.uploadBox.dropHover label svg {
  transform: translateY(-10px);
}

.uploadBox label p {
  font-size: 11px;
  color: rgb(0, 0, 0, 0.5);
  margin-top: 5px;
}

html[data-theme="dark"] .uploadBox label p {
  color: rgb(255, 255, 255, 0.5);
}

.uploadBox label p:first-of-type {
  font-size: 15px;
  font-weight: 700;
  color: var(--text-color) !important;
}

.links__wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.links__wrapper a {
  font-size: 14px;
}

.uploadBox label p:first-of-type span {
  color: var(--primary-color);
}

.content__section .container {
  max-width: 800px;
}

.content__wrapper {
  background-color: var(--forground-color);
  padding: 80px 0;
  border-radius: 1rem;
  padding: 30px;
}

.content__wrapper h1 {
  font-size: 2rem;
  text-align: center;
}

.content__wrapper h2 {
  text-align: center;
  font-size: 1.5rem;
}

.content__wrapper h2 span {
  color: var(--primary-color);
}

.content__wrapper > *:not(:first-child) {
  margin-top: 1.5rem;
}

.mt-60 {
  margin-top: 60px;
}

.content__wrapper .list > *:not(:first-child) {
  margin-top: 0.7rem;
}

.content__wrapper .list > * {
  padding-left: 20px;
}

.content__wrapper .list h3 {
  position: relative;
}

.content__wrapper .list h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: var(--text-color);
}

.right__side {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  gap: 30px;
  padding: 0 0 0 20px;
}

.right__side .btn {
  width: 100%;
}

.input__wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.btns__wrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.btns__wrapper .btn {
  flex: 1;
}

.arrow_down {
  display: none;
}

.input__wrapper input {
  flex: 1;
  height: 40px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  padding: 0 10px;
  font-size: 14px;
  transition: 0.3s all;
  background-color: var(--bg-color);
  outline: none;
  appearance: none;
  -moz-appearance: textfield;
}

.input__wrapper input:focus {
  border-color: var(--primary-color);
}

.input__wrapper input::placeholder {
  color: var(--text-color);
  opacity: 0.4;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input__wrapper select {
  height: 40px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  padding: 0 10px;
  font-size: 14px;
  transition: 0.3s all;
  background-color: var(--bg-color);
  outline: none;
  appearance: none;
  width: 70px;
  -moz-appearance: textfield;
  background-image: url("data:image/svg+xml,%3Csvg stroke='%23000' fill='%23000' stroke-width='0' viewBox='0 0 512 512' height='1em' width='1em' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M256 294.1L383 167c9.4-9.4 24.6-9.4 33.9 0s9.3 24.6 0 34L273 345c-9.1 9.1-23.7 9.3-33.1.7L95 201.1c-4.7-4.7-7-10.9-7-17s2.3-12.3 7-17c9.4-9.4 24.6-9.4 33.9 0l127.1 127z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

html[data-theme="dark"] .input__wrapper select {
  background-image: url("data:image/svg+xml,%3Csvg stroke='%23fff' fill='%23fff' stroke-width='0' viewBox='0 0 512 512' height='1em' width='1em' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M256 294.1L383 167c9.4-9.4 24.6-9.4 33.9 0s9.3 24.6 0 34L273 345c-9.1 9.1-23.7 9.3-33.1.7L95 201.1c-4.7-4.7-7-10.9-7-17s2.3-12.3 7-17c9.4-9.4 24.6-9.4 33.9 0l127.1 127z'%3E%3C/path%3E%3C/svg%3E");
}

.product__hund__embed {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.product__hund__embed a {
  display: inline-block;
}

.product__hund__embed img {
  width: 100%;
  max-width: 210px;
  object-fit: contain !important;
}

.footer {
  padding: 2rem 0 0rem;
  margin-top: 120px;
  border-top: none;
  text-align: left;
  background: var(--header-footer-color);
}

.comparison__modal_wrapper {
  width: 90%;
  max-width: 800px;
}

.footer__content__bottom {
  /* border-top: 1px solid var(--border-color); */
  /* padding-top: 2rem; */
  padding-block: 12px;
  width: 100%;
  
}
/* .copyright{
  width: 70%;
} */

.copyright p{
  color: var(--text-color);
  text-align: center;
}
/* .Policy {
  width: 30%;
  color: var(--text-color);
  display: flex;
  gap: 50px;
  justify-content: end;
  a{
    color: var(--text-color);
  }
} */
.footer__menu__list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.footer .container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  max-width: 1170px;
}

.footer .disclaimer {
  font-size: 0.8rem;
  color: var(--text-color);
  margin-top: 16px;
  margin-bottom: 10px;
}

.footer__menu__item {
  padding: 5px 5px;
  border-radius: 0.5rem;
  transition: 0.3s all;
  font-weight: 500;
  white-space: nowrap;
  margin: 0 5px;
  display: inline-block;
  font-size: 14px;
}

.footer__menu__item a,
.footer__menu__item span {
  color: var(--gray-color);
}

.social__share_icons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 10px;
  list-style: none;
  margin-left: -5px;
}

.social__share_icons a {
  font-size: 16px;
  margin: 5px;
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.3s all;
  color: var(--text-color);
  height: 34px;
  width: 34px;
}

.social__share_icons a:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

.footer__content__top .container {
  display: flex;
  max-width: 1170px;
  padding: 0px 10px 3rem;
  gap: 30px;
  border-bottom: 1px solid #00000045;
}

.site__info {
  width: 40%;
  padding-right: 30px;
}

.footer__menu {
  width: 20%;
}

.copyright {
  text-align: left;
  font-size: 14px;
}

.footer__menu h6 {
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 5px;
  text-align: left;
  padding-left: 8px;
}

.faqs__accordion .faqs__accordion__item {
  background-color: var(--forground-color);
  border-radius: 0.5rem;
  margin-bottom: 15px;
}

.faqs__accordion__item .faqs__accordion__item__header {
  padding: 15px 20px;
  cursor: pointer;
  position: relative;
}

.faqs__accordion__item .faqs__accordion__item__header h3 {
  font-size: 14px;
}

.faqs__accordion__item__header .faqs__accordion__item__header__icon {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' focusable='false' class='chakra-icon chakra-accordion__icon css-j2ph2z' aria-hidden='true'%3E%3Cpath fill='%23000' d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z'%3E%3C/path%3E%3C/svg%3E")
    center no-repeat;
  width: 20px;
  height: 20px;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.3s all;
  cursor: pointer;
}

.faqs__accordion__item.active .faqs__accordion__item__header__icon {
  transform: translateY(-50%) rotate(180deg);
}

html[data-theme="dark"]
  .faqs__accordion__item__header
  .faqs__accordion__item__header__icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' focusable='false' class='chakra-icon chakra-accordion__icon css-j2ph2z' aria-hidden='true'%3E%3Cpath fill='%23fff' d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z'%3E%3C/path%3E%3C/svg%3E");
}

.faqs__accordion__item__body {
  display: none;
}

.faqs h2 {
  text-align: center;
  margin-bottom: 40px;
}

.faqs .container {
  max-width: 900px;
}

.faqs__accordion__item__body {
  padding: 0 20px 20px;
}

.faqs__accordion__item__body p {
  font-size: 14px;
}

.mobile__apps__links {
  margin-bottom: 60px;
  margin-top: 60px;
}

.mobile__apps__links .container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.mobile__apps__links .container a {
  display: flex;
}

.mobile__apps__links .container img {
  max-width: 170px;
  height: 50px;
  object-fit: contain;
}

.mobile__apps .container {
  display: flex;
  justify-content: center;
}

.mobile__apps__links__wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mobile__apps__links__wrapper a {
  display: flex;
  transition: 0.3s all;
}

.mobile__apps__links__wrapper a:hover {
  transform: translateY(-3px);
}

.mobile__apps__links__wrapper img {
  max-width: 180px;
}

.mobile__apps__links__wrapper .appstore img {
  max-width: 170px;
}

.page__content {
  padding: 50px 0 0;
}

.page__content .container {
  max-width: 1050px;
}

.page__content h1 {
  font-size: 2rem;
}

.page__content__wrapper > *:not(:last-child) {
  margin-bottom: 20px;
}

.max-750 .container {
  max-width: 750px;
}

.highlighted {
  border-radius: 0.5rem;
  background-color: #ebebf0;
  padding: 20px;
  text-align: center;
}

html[data-theme="dark"] .highlighted {
  background-color: #181820;
}

video {
  max-width: 100%;
  border-radius: 0.5rem;
}

.bookmark__btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 10px 20px;
  border: 1px dashed var(--primary-color);
  color: var(--primary-color);
  font-weight: 600;
  transition: 0.3s all;
  cursor: pointer;
  margin-left: auto;
  margin-right: auto;
}

.pdf__preview {
  position: relative;
  padding-bottom: 70%;
  border-radius: 20px;
  overflow: hidden;
}

.pdf__preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.content__page_section .container {
  max-width: 1100px;
  text-align: left;
}

.content__page_section h2 {
  text-align: left;
}

.content__section li {
  margin-left: 17px;
}

.content__page_section h1 {
  font-size: 2rem;
}

.form__group {
  width: 100%;
  margin-bottom: 20px;
}

.form__group input,
.form__group textarea {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 10px 15px;
  font-size: 14px;
  transition: 0.3s all;
  background-color: var(--bg-color);
}

.form__group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.selected__image {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 2;
  padding: 5px;
  display: none;
  align-items: center;
  justify-content: center;
}

.selected__image .orignalSize {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  background-color: #000000a6;
  padding: 5px 10px;
  border-radius: 13px 0px 13px 0px;
  position: absolute;
  bottom: 5px;
  right: 5px;
}

.jq-icon-error {
  background-color: #9f0404;
  color: #f2dede;
  font-size: 15px;
  border-color: #ebccd1;
  background-size: 19px;
  border-radius: 8px;
  padding-left: 37px;
  box-shadow: 0 0 15px 0 #0000006e;
}

.selected__image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  border-radius: 15px;
  width: 100%;
  height: 100%;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 10px 20px;
  border: 1px solid var(--primary-color);
  background-color: var(--primary-color);
  color: var(--bg-color);
  font-weight: 600;
  transition: 0.3s all;
  cursor: pointer;
}

.btn:hover {
  filter: brightness(0.8);
}

.btn.outline {
  background-color: transparent;
  color: var(--primary-color);
}

.btn.outline:hover {
  background-color: var(--primary-color);
  color: var(--bg-color);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.contact__form .btn {
  min-width: 150px;
}

.success__msg {
  display: none;
}

.max-800 .container {
  max-width: 800px;
}

.text-left {
  text-align: left !important;
}

.addblock_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--forground-color);
  z-index: 100000;
  display: none;
}

.addblock_popup > div {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.addblock_popup > div > div {
  max-width: 650px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  padding: 10px;
  margin: auto;
}

.addblock_popup #animation__image {
  width: 100%;
  max-width: 450px;
  margin-top: -100px;
}

.addblock_popup h2 {
  font-size: 2rem;
}

.addblock_popup p {
  font-size: 1.1rem;
  margin-top: 10px;
}

.popup__close__button {
  background-color: var(--bg-color);
  border: 0;
  border-radius: 5px;
  width: 35px;
  height: 35px;
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  z-index: 2;
}

.links__section {
  margin-top: 30px;
}

.links__section .container {
  max-width: 800px;
  text-align: center;
}

.links__section .info {
  font-size: 13px;
}

.faqs .container {
  max-width: 800px;
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 1255px) {
  .footer .container {
    max-width: 1000px;
  }
}

@media (max-width: 1060px) {
  .footer .container {
    max-width: 980px;
    padding-left: 40px;
    padding-right: 40px;
  }
}

@media (max-width: 845px) {
  .footer__content__top .container {
    flex-wrap: wrap;
  }

  .site__info {
    width: 100%;
    padding-left: 8px;
  }

  .footer__menu {
    width: calc(34% - 30px);
  }
}

@media (max-width: 800px) {
  .hero__wrapper,
  .result__wrapper {
    flex-direction: column;
  }

  .hero__section .left__side,
  .result__section .left__side {
    width: 100%;
  }

  .hero__section .container,
  .result__section .container {
    max-width: 472px;
  }

  .hero__section .right__side,
  .result__section .right__side {
    width: 100%;
    padding: 30px 0 0 0;
    flex-direction: column;
  }

  .hero__section .right__side {
    gap: 20px;
  }

  .hero__section .right__side .btn {
    max-width: 100%;
  }

  .hero__wrapper {
    padding: 20px 20px 30px;
  }

  .result__info__wrapper {
    margin-bottom: 0;
  }

  .hero__section,
  .result__section {
    padding: 60px 0 0;
  }
}

@media (min-width: 768px) {
  .header__menu_dropdown {
    display: block !important;
  }
}

@media (max-width: 767px) {
  .hero__section h1 {
    text-align: center;
    font-size: 20px;
  }

  .toogle_menu {
    display: block;
  }

  .header__menu {
    position: fixed;
    height: 100vh;
    top: -20px;
    left: -316px;
    width: 300px;
    border-right: 1px solid var(--border-color);
    background-color: var(--bg-color);
    z-index: 1002;
    transition: 0.3s all;
    overflow: auto;
  }

  body.open__menu .header__menu {
    left: -16px;
  }

  .toggle_button {
    margin-left: auto;
  }

  .mobile_only {
    display: block;
  }

  .header__menu .header__logo {
    padding: 28px;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    background-color: var(--bg-color);
    z-index: 4;
    top: 0;
  }

  .header__menu_list {
    flex-direction: column;
    padding: 20px;
  }

  .header__menu_list li {
    text-align: left;
    align-items: flex-start;
    width: 100%;
    margin: 0;
    margin-bottom: 10px;
  }

  .header__menu_list li a {
    padding: 7px 15px !important;
    border-radius: 5px;
    display: block;
  }

  .header__menu_list li.active > a {
    background-color: var(--forground-color);
  }

  .header__menu_list li span {
    display: none;
  }

  .header__menu_dropdown {
    position: static;
    opacity: 1;
    visibility: visible;
    background-color: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    border: none;
    width: 100%;
    transition: none;
  }

  .has__submenu .header__menu_dropdown {
    display: none;
  }

  .has__submenu {
    position: relative;
  }

  .has__submenu .arrow_down {
    content: "";
    position: absolute;
    right: -10px;
    top: -2px;
    height: 40px;
    width: 50px;
    transition: 0.3s all;
    background-image: url("data:image/svg+xml,%3Csvg stroke='%23000' fill='%23000' stroke-width='0' viewBox='0 0 512 512' height='1em' width='1em' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M256 294.1L383 167c9.4-9.4 24.6-9.4 33.9 0s9.3 24.6 0 34L273 345c-9.1 9.1-23.7 9.3-33.1.7L95 201.1c-4.7-4.7-7-10.9-7-17s2.3-12.3 7-17c9.4-9.4 24.6-9.4 33.9 0l127.1 127z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 15px;
    z-index: 1;
    display: flex;
    cursor: pointer;
  }

  html[data-theme="dark"] .has__submenu .arrow_down {
    background-image: url("data:image/svg+xml,%3Csvg stroke='%23fff' fill='%23fff' stroke-width='0' viewBox='0 0 512 512' height='1em' width='1em' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M256 294.1L383 167c9.4-9.4 24.6-9.4 33.9 0s9.3 24.6 0 34L273 345c-9.1 9.1-23.7 9.3-33.1.7L95 201.1c-4.7-4.7-7-10.9-7-17s2.3-12.3 7-17c9.4-9.4 24.6-9.4 33.9 0l127.1 127z'%3E%3C/path%3E%3C/svg%3E");
  }

  .has__submenu.active .arrow_down {
    transform: rotate(180deg);
  }

  .close__overlay {
    position: fixed;
    top: -20px;
    left: -16px;
    width: calc(100% + 32px);
    height: 100vh;
    z-index: 1001;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
  }

  body.open__menu .close__overlay {
    display: block;
  }

  .content__wrapper {
    padding: 20px;
  }

  .content__wrapper h2 {
    font-size: 20px;
  }

  .page__content h1,
  .content__wrapper h1 {
    font-size: 1.5rem;
  }

  .footer {
    padding: 3.5rem 0 0rem;
  }

  .footer .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .footer__content__top .container {
    padding-bottom: 3.5rem;
  }

  .footer__content__bottom {
    padding-left: 7px;
    padding-right: 7px;
  }
}

@media (max-width: 575px) {
  .site__info {
    width: 100%;
  }

  .footer__menu {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .comparison__wrapper .icv__img-a {
    width: 302px !important;
  }

  .comparison__wrapper .icv__img-b {
    width: 302px !important;
  }

  .comparison__wrapper,
  .uploadBox {
    height: 260px;
  }

  .hero__section .container,
  .result__section .container {
    max-width: 375px;
  }
}
.policy h3{
  margin-bottom: 20px;
}
.policy p{
  margin-bottom: 12px;
}
.policy .center{
  text-align: center;
  display: block;
  margin-block: 40px;
}
.contact-us{
  min-height: 40vh;
}
.contact-us .center{
  text-align: center;
  display: block;
  margin-block: 40px;
}
.contact-us .center h1{
  font-size: 2rem;
}
/================ CUSTOM GRID CSS FILE START HERE =====================/

.container {
    max-width: 1300px;
    margin: 0 auto;
}

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-sm-1 {}

.col-sm-2 {}

.col-sm-3 {}

.col-sm-4 {}

.col-sm-5 {}

.col-sm-6 {}

.col-sm-7 {}

.col-sm-8 {}

.col-sm-9 {}

.col-sm-10 {}

.col-sm-11 {}

.col-sm-12 {}

.nogrid_padding {
    padding: 0 !important;
}

.pagetitle_sec {
    padding: 9px;
    background: #242560;
    color: #fff;
}

.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12 {
    padding-right: 15px;
    padding-left: 15px;
}

@media only screen and (min-width: 768px) {
    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }
    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }
    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%;
    }
    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
    }
    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }
    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%;
    }
    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }
    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%;
    }
    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12 {
        position: relative;
        width: 100%;
        min-height: 1px;
    }
}

@media only screen and (min-width: 1024px) {}

@media only screen and (min-width: 1200px) {}

@media only screen and (min-width: 1300px) {}

@media only screen and (min-width: 1400px) {}

@media only screen and (min-width: 1920px) {}

@media only screen and (max-width: 767px) {
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12 {
        position: relative;
        width: 100%;
    }
    .container {
        padding: 0 15px;
    }
}

@media only screen and (max-width: 480px) {}

@media only screen and (min-width: 480px) and (max-width: 767px) {}

.no_list.list h3::before {
    content: none;
    

}


/*================ CUSTOM GRID CSS FILE START HERE =====================*Thank you for your business! We look forward to working with you again.
